-- Create missing tracking tables for StreamDB Online
-- These tables support the storage service functionality

-- User sessions table for session management
CREATE TABLE IF NOT EXISTS user_sessions (
  id VARCHAR(255) PRIMARY KEY,
  user_id VARCHAR(36) NULL,
  session_data TEXT NOT NULL,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  expires_at DATETIME NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_expires_at (expires_at),
  INDEX idx_ip_address (ip_address)
);

-- Ad blocker tracking table
CREATE TABLE IF NOT EXISTS ad_blocker_tracking (
  id INT AUTO_INCREMENT PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(36) NULL,
  last_shown_timestamp BIGINT DEFAULT 0,
  dismiss_count INT DEFAULT 0,
  user_agent TEXT NULL,
  ip_address VARCHAR(45) NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_session (session_id),
  INDEX idx_user_id (user_id),
  INDEX idx_ip_address (ip_address)
);



-- Security logs table for audit trail
CREATE TABLE IF NOT EXISTS security_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(36) NULL,
  event_type VARCHAR(100) NOT NULL,
  severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  details TEXT NULL,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  timestamp BIGINT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_session_id (session_id),
  INDEX idx_user_id (user_id),
  INDEX idx_event_type (event_type),
  INDEX idx_severity (severity),
  INDEX idx_timestamp (timestamp),
  INDEX idx_ip_address (ip_address)
);

-- Auth tokens table for secure token management
CREATE TABLE IF NOT EXISTS auth_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  token_hash VARCHAR(64) NOT NULL,
  session_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  token_type ENUM('access', 'refresh', 'api') DEFAULT 'access',
  expires_at DATETIME NOT NULL,
  is_revoked BOOLEAN DEFAULT FALSE,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_token_hash (token_hash),
  INDEX idx_session_id (session_id),
  INDEX idx_user_id (user_id),
  INDEX idx_token_type (token_type),
  INDEX idx_expires_at (expires_at),
  INDEX idx_is_revoked (is_revoked)
);

-- Insert initial data or update existing records
-- This ensures the tables are ready for use

-- Show created tables
SELECT 'Tables created successfully' as status;
SHOW TABLES LIKE '%tracking%';
SHOW TABLES LIKE '%sessions%';
SHOW TABLES LIKE '%attempts%';
SHOW TABLES LIKE '%logs%';
SHOW TABLES LIKE '%tokens%';
