/**
 * Dynamic Content Sections API
 * Handles all operations for dynamic content sections management
 */

import { MediaItem } from '@/types/media';

// API Configuration
const API_BASE_URL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3001/api';

/**
 * Content Section Interface
 */
export interface ContentSection {
  id: number;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  display_order: number;
  is_active: boolean;
  show_in_navigation: boolean;
  show_on_homepage: boolean;
  max_items_homepage: number;
  content_types: string[];
  filter_rules: Record<string, any>;
  content_count?: number;
  categories?: Category[];
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  type: string;
  slug: string;
  description: string;
  is_active: boolean;
  is_default?: boolean;
}

export interface SectionFormData {
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  display_order: number;
  is_active: boolean;
  show_in_navigation: boolean;
  show_on_homepage: boolean;
  max_items_homepage: number;
  content_types: string[];
  filter_rules: Record<string, any>;
  category_ids: number[];
}

/**
 * Generic API request handler
 */
async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Sections API service
 */
export const sectionsAPI = {
  /**
   * Get all content sections
   */
  async getSections(params: {
    active_only?: boolean;
    include_categories?: boolean;
  } = {}): Promise<{ success: boolean; data: ContentSection[]; total: number; message?: string }> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.active_only) queryParams.append('active_only', 'true');
      if (params.include_categories) queryParams.append('include_categories', 'true');

      const result = await apiRequest<{ success: boolean; data: ContentSection[]; total: number }>(`/sections?${queryParams}`);
      return result;
    } catch (error) {
      console.error('Failed to fetch sections:', error);
      return {
        success: false,
        data: [],
        total: 0,
        message: error instanceof Error ? error.message : 'Failed to fetch sections',
      };
    }
  },

  /**
   * Get single section by ID or slug
   */
  async getSection(identifier: string | number): Promise<{ success: boolean; data?: ContentSection; message?: string }> {
    try {
      const result = await apiRequest<{ success: boolean; data: ContentSection }>(`/sections/${identifier}`);
      return result;
    } catch (error) {
      console.error('Failed to fetch section:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch section',
      };
    }
  },

  /**
   * Create new content section
   */
  async createSection(formData: SectionFormData): Promise<{ success: boolean; id?: number; message: string }> {
    try {
      const result = await apiRequest<{ success: boolean; id: number; message: string }>('/sections', {
        method: 'POST',
        body: JSON.stringify(formData),
      });

      return result;
    } catch (error) {
      console.error('Failed to create section:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create section',
      };
    }
  },

  /**
   * Update existing content section
   */
  async updateSection(id: number, formData: Partial<SectionFormData>): Promise<{ success: boolean; message: string }> {
    try {
      const result = await apiRequest<{ success: boolean; message: string }>(`/sections/${id}`, {
        method: 'PUT',
        body: JSON.stringify(formData),
      });

      return result;
    } catch (error) {
      console.error('Failed to update section:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update section',
      };
    }
  },

  /**
   * Delete content section
   */
  async deleteSection(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const result = await apiRequest<{ success: boolean; message: string }>(`/sections/${id}`, {
        method: 'DELETE',
      });

      return result;
    } catch (error) {
      console.error('Failed to delete section:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete section',
      };
    }
  },

  /**
   * Get content for a specific section
   */
  async getSectionContent(id: number, params: {
    page?: number;
    limit?: number;
    published_only?: boolean;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  } = {}): Promise<{
    success: boolean;
    data: MediaItem[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    section?: { id: number; name: string; slug: string };
    message?: string;
  }> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.published_only !== undefined) queryParams.append('published_only', params.published_only.toString());
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.sort_order) queryParams.append('sort_order', params.sort_order);

      const result = await apiRequest<{
        success: boolean;
        data: MediaItem[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        section: { id: number; name: string; slug: string };
      }>(`/sections/${id}/content?${queryParams}`);

      return result;
    } catch (error) {
      console.error('Failed to fetch section content:', error);
      return {
        success: false,
        data: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
        message: error instanceof Error ? error.message : 'Failed to fetch section content',
      };
    }
  },

  /**
   * Update section display order
   */
  async reorderSections(sections: { id: number; display_order: number }[]): Promise<{ success: boolean; message: string }> {
    try {
      const result = await apiRequest<{ success: boolean; message: string }>('/sections/reorder', {
        method: 'PUT',
        body: JSON.stringify({ sections }),
      });

      return result;
    } catch (error) {
      console.error('Failed to reorder sections:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to reorder sections',
      };
    }
  },
};

/**
 * Categories API service (for section management)
 */
export const categoriesAPI = {
  /**
   * Get all categories
   */
  async getCategories(): Promise<{ success: boolean; data: Category[]; message?: string }> {
    try {
      const result = await apiRequest<{ success: boolean; data: Category[] }>('/categories');
      return result;
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      return {
        success: false,
        data: [],
        message: error instanceof Error ? error.message : 'Failed to fetch categories',
      };
    }
  },
};

/**
 * Utility functions for sections
 */
export const sectionUtils = {
  /**
   * Generate slug from name
   */
  generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  },

  /**
   * Validate section form data
   */
  validateSectionForm(data: Partial<SectionFormData>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Section name is required');
    }

    if (!data.slug || data.slug.trim().length === 0) {
      errors.push('Section slug is required');
    }

    if (data.slug && !/^[a-z0-9-]+$/.test(data.slug)) {
      errors.push('Slug can only contain lowercase letters, numbers, and hyphens');
    }

    if (data.max_items_homepage && (data.max_items_homepage < 1 || data.max_items_homepage > 100)) {
      errors.push('Max items on homepage must be between 1 and 100');
    }

    if (data.display_order && data.display_order < 0) {
      errors.push('Display order must be a non-negative number');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Get default section form data
   */
  getDefaultFormData(): SectionFormData {
    return {
      name: '',
      slug: '',
      description: '',
      icon: 'Folder',
      color: '#3b82f6',
      display_order: 0,
      is_active: true,
      show_in_navigation: true,
      show_on_homepage: true,
      max_items_homepage: 20,
      content_types: ['movie', 'series'],
      filter_rules: {},
      category_ids: [],
    };
  },
};
